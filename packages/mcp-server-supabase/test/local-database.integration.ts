import { describe, test, expect, beforeAll } from 'vitest';
import {
  createMemoryMasterTestClient,
  testDatabaseConnection,
  validateTestEnvironment,
  validateTestIsolation,
  getTestDatabaseStats,
  testConfig
} from './test-config';
import { validateMockSetup } from './mocks';

/**
 * Integration tests for local Supabase database connection
 * These tests validate that the testing framework can connect to the local database
 * and perform basic read-only operations as required for integration testing.
 */

describe('Local Database Integration', () => {
  beforeAll(async () => {
    // Validate test environment before running tests
    const validation = validateTestEnvironment();
    if (!validation.valid) {
      throw new Error(`Test environment validation failed: ${validation.errors.join(', ')}`);
    }

    // Validate mock setup
    const mockValidation = validateMockSetup();
    if (!mockValidation.valid) {
      console.warn('Mock setup issues detected:', mockValidation.issues);
    }

    // Validate test isolation
    const isolationValidation = validateTestIsolation();
    if (!isolationValidation.isolated) {
      console.warn('Test isolation issues detected:', isolationValidation.issues);
    }
  });

  test('can connect to local Supabase instance', async () => {
    const supabase = createMemoryMasterTestClient();

    // Test connection with retry logic
    const connectionResult = await testDatabaseConnection(supabase, 'memory_master');
    expect(connectionResult.connected).toBe(true);
    if (!connectionResult.connected) {
      throw new Error(`Database connection failed: ${connectionResult.error}`);
    }

    // Test basic connection by querying a known table
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    expect(error).toBeNull();
    expect(data).toBeDefined();
  }, testConfig.testTimeout);

  test('can read from memory_master schema tables', async () => {
    const supabase = createMemoryMasterTestClient();

    // Test reading from multiple tables to validate schema access
    const [usersResult, appsResult, memoriesResult] = await Promise.all([
      supabase.from('users').select('*', { count: 'exact', head: true }),
      supabase.from('apps').select('*', { count: 'exact', head: true }),
      supabase.from('memories').select('*', { count: 'exact', head: true })
    ]);

    expect(usersResult.error).toBeNull();
    expect(appsResult.error).toBeNull();
    expect(memoriesResult.error).toBeNull();

    expect(usersResult.count).toBeGreaterThan(0);
    expect(appsResult.count).toBeGreaterThan(0);
    expect(memoriesResult.count).toBeGreaterThan(0);
  }, testConfig.testTimeout);

  test('can validate JSON fields in database', async () => {
    const supabase = createMemoryMasterTestClient();

    // Test that JSON fields are properly formatted
    const { data, error } = await supabase
      .from('memories')
      .select('id, metadata')
      .not('metadata', 'is', null)
      .limit(5);

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(Array.isArray(data)).toBe(true);

    if (data && data.length > 0) {
      // Validate that metadata is valid JSON
      data.forEach(record => {
        expect(record.metadata).toBeDefined();
        expect(typeof record.metadata).toBe('object');
      });
    }
  }, testConfig.testTimeout);

  test('can validate referential integrity', async () => {
    const supabase = createMemoryMasterTestClient();

    // Test that foreign key relationships are maintained
    const { data: memories, error: memoriesError } = await supabase
      .from('memories')
      .select('id, user_id, app_id')
      .limit(10);

    expect(memoriesError).toBeNull();
    expect(memories).toBeDefined();

    if (memories && memories.length > 0) {
      // Check that referenced users exist
      const userIds = [...new Set(memories.map(m => m.user_id).filter(Boolean))];
      if (userIds.length > 0) {
        const { data: users, error: usersError } = await supabase
          .from('users')
          .select('id')
          .in('id', userIds);

        expect(usersError).toBeNull();
        expect(users).toBeDefined();
        expect(users?.length).toBe(userIds.length);
      }

      // Check that referenced apps exist
      const appIds = [...new Set(memories.map(m => m.app_id).filter(Boolean))];
      if (appIds.length > 0) {
        const { data: apps, error: appsError } = await supabase
          .from('apps')
          .select('id')
          .in('id', appIds);

        expect(appsError).toBeNull();
        expect(apps).toBeDefined();
        expect(apps?.length).toBe(appIds.length);
      }
    }
  }, testConfig.testTimeout);

  test('database performance is acceptable', async () => {
    const supabase = createMemoryMasterTestClient();

    const startTime = Date.now();
    const { data, error } = await supabase
      .from('memories')
      .select('id, content, created_at')
      .order('created_at', { ascending: false })
      .limit(100);
    const queryTime = Date.now() - startTime;

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(queryTime).toBeLessThan(testConfig.performanceThreshold); // Query should complete within acceptable time
  }, testConfig.testTimeout);

  test('can retrieve database statistics', async () => {
    const stats = await getTestDatabaseStats();

    expect(stats.users).toBeGreaterThan(0);
    expect(stats.apps).toBeGreaterThan(0);
    expect(stats.memories).toBeGreaterThan(0);
    expect(stats.schemas).toContain('memory_master');
    expect(stats.schemas).toContain('public');
  }, testConfig.testTimeout);

  test('external dependencies are properly mocked', async () => {
    // Verify that external network calls are intercepted
    const mockValidation = validateMockSetup();
    expect(mockValidation.valid).toBe(true);

    // Verify test isolation
    const isolationValidation = validateTestIsolation();
    expect(isolationValidation.isolated).toBe(true);

    // Test that time is mocked (deterministic)
    const time1 = Date.now();
    const time2 = Date.now();
    expect(time2).toBe(time1); // Should be the same in mocked time

    // Test that crypto operations are available
    expect(crypto.randomUUID).toBeDefined();
    expect(typeof crypto.randomUUID()).toBe('string');
  }, testConfig.testTimeout);

  test('database operations do not make external network calls', async () => {
    const supabase = createMemoryMasterTestClient();

    // This should use mocked/local database, not external services
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    // The operation should complete without making real network requests
    expect(error).toBeNull();
    expect(data).toBeDefined();

    // Verify we're not hitting real external services
    expect(testConfig.supabaseUrl).not.toContain('supabase.co');
  }, testConfig.testTimeout);
});
