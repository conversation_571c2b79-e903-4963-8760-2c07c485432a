import { config } from 'dotenv';
import { statSync } from 'fs';
import { webcrypto } from 'crypto';
import { beforeEach, afterEach } from 'vitest';
import './test/extensions.js';
import { setupAllMocks, validateMockSetup } from './test/mocks.js';

// Polyfill crypto for Node.js test environment
if (!globalThis.crypto) {
  globalThis.crypto = webcrypto as any;
}

// Polyfill File API for Node.js test environment
if (!globalThis.File) {
  globalThis.File = class File {
    constructor(public chunks: BlobPart[], public name: string, public options?: FilePropertyBag) {
      this.name = name;
      this.type = options?.type || '';
      this.lastModified = options?.lastModified || Date.now();
    }
    
    type: string;
    lastModified: number;
    
    async text(): Promise<string> {
      return this.chunks.join('');
    }
    
    async arrayBuffer(): Promise<ArrayBuffer> {
      const text = await this.text();
      return new TextEncoder().encode(text).buffer;
    }
  } as any;
}

if (!process.env.CI) {
  const envPath = '.env.local';
  statSync(envPath);
  config({ path: envPath });
}

// Global test setup for external dependency mocking
let globalMockSetup: ReturnType<typeof setupAllMocks> | null = null;

beforeEach(() => {
  // Setup comprehensive mocks for each test
  globalMockSetup = setupAllMocks({
    enableNetworkMocks: true,
  });

  // Validate mock setup
  const validation = validateMockSetup();
  if (!validation.valid) {
    console.warn('Mock setup validation failed:', validation.issues);
  }
});

afterEach(() => {
  // Cleanup mocks after each test
  if (globalMockSetup) {
    globalMockSetup.cleanup();
    globalMockSetup = null;
  }
});
